<template>
  <el-dialog
    v-model="visible"
    title="Window Arrangement"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    append-to-body
    class="el-dialog--beautify arrange-dialog"
    @closed="onClosed"
  >
    <div class="arrange-container">
      <div class="control-panel">
        <div class="control-section">
          <el-dropdown type="primary" @command="addWidget">
            <el-button type="primary">
              <template #icon>
                <el-icon>
                  <el-icon-plus></el-icon-plus>
                </el-icon>
              </template>

              Add Widget

              <el-icon class="el-icon--right">
                <el-icon-arrow-down />
              </el-icon>
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="global"
                  :disabled="hasGlobalWidget"
                >
                  <el-icon><Setting /></el-icon>
                  Global Configuration
                </el-dropdown-item>
                <el-dropdown-item
                  v-for="device in availableDevices"
                  :key="device.id"
                  :command="device.id"
                >
                  <el-icon><Monitor /></el-icon>
                  {{ device.name || device.id }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <div class="control-section">
          <el-button
            type="default"
            :icon="Refresh"
            @click="resetLayout"
          >
            Reset Layout
          </el-button>
        </div>

        <div class="control-section">
          <el-button
            type="danger"
            :icon="Delete"
            :disabled="arrangedWidgets.length === 0"
            @click="clearAllWidgets"
          >
            Clear All
          </el-button>
        </div>
      </div>

      <!-- Arrangement Area -->
      <div ref="arrangementAreaRef" class="arrangement-area">
        <div
          ref="screenContainerRef"
          class="screen-container"
          :style="screenContainerStyle"
        >
          <VueDraggableResizable
            v-for="widget in arrangedWidgets"
            :key="widget.id"
            :x="widget.x"
            :y="widget.y"
            :w="widget.width"
            :h="widget.height"
            :min-width="50"
            :min-height="50"
            :parent="true"
            :grid="[5, 5]"
            class="widget-window" :class="[widget.type === 'global' ? 'global-widget' : 'device-widget']"
            @dragging="(x, y) => onWidgetDragging(widget.id, { x, y })"
            @resizing="(x, y, w, h) => onWidgetResizing(widget.id, { x, y, width: w, height: h })"
            @drag-stop="(x, y) => onWidgetDragStop(widget.id, { x, y })"
            @resize-stop="(x, y, w, h) => onWidgetResizeStop(widget.id, { x, y, width: w, height: h })"
          >
            <div class="widget-content">
              <div class="widget-header">
                <div class="widget-title">
                  <el-icon>
                    <Setting v-if="widget.type === 'global'" />
                    <Monitor v-else />
                  </el-icon>
                  <span class="widget-name">
                    {{ widget.type === 'global' ? 'Global' : (widget.name || widget.id) }}
                  </span>
                </div>
                <el-button
                  type="danger"
                  :icon="Close"
                  size="small"
                  circle
                  class="remove-btn"
                  @click="removeWidget(widget.id)"
                />
              </div>
              <div class="widget-body">
                <div class="widget-info">
                  <p>Size: {{ Math.round(widget.realWidth) }}x{{ Math.round(widget.realHeight) }}</p>
                  <p>Position: {{ Math.round(widget.realX) }}, {{ Math.round(widget.realY) }}</p>
                </div>
              </div>
            </div>
          </VueDraggableResizable>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="close">
        Cancel
      </el-button>
      <el-button type="primary" @click="saveLayout">
        Save Layout
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, nextTick, ref } from 'vue'
import { Close, Delete, Monitor, Refresh, Setting } from '@element-plus/icons-vue'
import VueDraggableResizable from 'vue-draggable-resizable'
import 'vue-draggable-resizable/style.css'
import { usePreferenceStore } from '$/store/preference/index.js'

const preferenceStore = usePreferenceStore()

const visible = ref(false)
const arrangementAreaRef = ref(null)
const screenContainerRef = ref(null)

// Screen and scaling properties
const screenInfo = ref({
  width: 1920,
  height: 1080,
  scaleFactor: 1,
})

const containerDimensions = ref({
  width: 800,
  height: 600,
})

// Widget management
const allDevices = ref([])
const arrangedWidgets = ref([])

// 检查是否已有全局widget
const hasGlobalWidget = computed(() => {
  return arrangedWidgets.value.some(w => w.type === 'global')
})

// 可用设备列表（排除已添加的设备widget）
const availableDevices = computed(() => {
  const arrangedDeviceIds = arrangedWidgets.value
    .filter(w => w.type === 'device')
    .map(w => w.deviceId)

  return allDevices.value.filter(d => !arrangedDeviceIds.includes(d.id))
})

// Computed styles
const screenContainerStyle = computed(() => {
  const aspectRatio = screenInfo.value.width / screenInfo.value.height
  const containerWidth = containerDimensions.value.width
  const containerHeight = containerDimensions.value.height

  let width, height

  if (containerWidth / containerHeight > aspectRatio) {
    // Container is wider than screen aspect ratio
    height = containerHeight
    width = height * aspectRatio
  }
  else {
    // Container is taller than screen aspect ratio
    width = containerWidth
    height = width / aspectRatio
  }

  return {
    width: `${width}px`,
    height: `${height}px`,
    backgroundColor: '#f5f5f5',
    border: '2px solid #ddd',
    position: 'relative',
    margin: '0 auto',
  }
})

// Scaling functions
const scaleToContainer = (realValue, isWidth = true) => {
  const screenDimension = isWidth ? screenInfo.value.width : screenInfo.value.height
  const containerDimension = isWidth
    ? Number.parseInt(screenContainerStyle.value.width)
    : Number.parseInt(screenContainerStyle.value.height)

  return (realValue / screenDimension) * containerDimension
}

const scaleToReal = (containerValue, isWidth = true) => {
  const screenDimension = isWidth ? screenInfo.value.width : screenInfo.value.height
  const containerDimension = isWidth
    ? Number.parseInt(screenContainerStyle.value.width)
    : Number.parseInt(screenContainerStyle.value.height)

  return (containerValue / containerDimension) * screenDimension
}

// Device management functions
const loadDevices = () => {
  const devices = window.appStore.get('device') || {}
  allDevices.value = Object.values(devices).map(device => ({
    ...device,
    name: device.name || device.model?.split(':')[1] || device.id,
  }))
}

const loadLayout = () => {
  arrangedWidgets.value = []

  const scrcpy = window.appStore.get('scrcpy')

  // 加载全局配置widget
  const globalConfig = scrcpy.global || {}
  if (globalConfig['--window-width'] && globalConfig['--window-height']) {
    const realWidth = Number.parseInt(globalConfig['--window-width']) || 300
    const realHeight = Number.parseInt(globalConfig['--window-height']) || 600
    const realX = Number.parseInt(globalConfig['--window-x']) || 0
    const realY = Number.parseInt(globalConfig['--window-y']) || 0

    arrangedWidgets.value.push({
      id: 'global',
      type: 'global',
      name: 'Global',
      x: scaleToContainer(realX, true),
      y: scaleToContainer(realY, false),
      width: scaleToContainer(realWidth, true),
      height: scaleToContainer(realHeight, false),
      realX,
      realY,
      realWidth,
      realHeight,
    })
  }

  // 加载设备特定配置widgets
  allDevices.value.forEach((device) => {
    const deviceConfig = scrcpy[device.id] || {}

    if (deviceConfig['--window-width'] && deviceConfig['--window-height']) {
      const realWidth = Number.parseInt(deviceConfig['--window-width']) || 300
      const realHeight = Number.parseInt(deviceConfig['--window-height']) || 600
      const realX = Number.parseInt(deviceConfig['--window-x']) || 0
      const realY = Number.parseInt(deviceConfig['--window-y']) || 0

      arrangedWidgets.value.push({
        id: device.id,
        type: 'device',
        deviceId: device.id,
        name: device.name || device.model?.split(':')[1] || device.id,
        x: scaleToContainer(realX, true),
        y: scaleToContainer(realY, false),
        width: scaleToContainer(realWidth, true),
        height: scaleToContainer(realHeight, false),
        realX,
        realY,
        realWidth,
        realHeight,
      })
    }
  })
}

// Widget management methods
const addWidget = (command) => {
  if (command === 'global') {
    // 添加全局widget
    if (hasGlobalWidget.value) {
      ElMessage.warning('Global widget already exists')
      return
    }

    const globalConfig = window.appStore.get('scrcpy.global') || {}
    const defaultWidth = Number.parseInt(globalConfig['--window-width']) || 300
    const defaultHeight = Number.parseInt(globalConfig['--window-height']) || 600
    const defaultX = Number.parseInt(globalConfig['--window-x']) || (arrangedWidgets.value.length * 50)
    const defaultY = Number.parseInt(globalConfig['--window-y']) || (arrangedWidgets.value.length * 50)

    arrangedWidgets.value.push({
      id: 'global',
      type: 'global',
      name: 'Global',
      x: scaleToContainer(defaultX, true),
      y: scaleToContainer(defaultY, false),
      width: scaleToContainer(defaultWidth, true),
      height: scaleToContainer(defaultHeight, false),
      realX: defaultX,
      realY: defaultY,
      realWidth: defaultWidth,
      realHeight: defaultHeight,
    })
  }
  else {
    // 添加设备widget
    const device = allDevices.value.find(d => d.id === command)
    if (!device) {
      ElMessage.error('Device not found')
      return
    }

    const deviceConfig = window.appStore.get(`scrcpy.${command}`) || {}
    const globalConfig = window.appStore.get('scrcpy.global') || {}

    // 设备配置优先，全局配置作为回退
    const config = { ...globalConfig, ...deviceConfig }

    const defaultWidth = Number.parseInt(config['--window-width']) || 300
    const defaultHeight = Number.parseInt(config['--window-height']) || 600
    const defaultX = Number.parseInt(config['--window-x']) || (arrangedWidgets.value.length * 50)
    const defaultY = Number.parseInt(config['--window-y']) || (arrangedWidgets.value.length * 50)

    arrangedWidgets.value.push({
      id: device.id,
      type: 'device',
      deviceId: device.id,
      name: device.name || device.model?.split(':')[1] || device.id,
      x: scaleToContainer(defaultX, true),
      y: scaleToContainer(defaultY, false),
      width: scaleToContainer(defaultWidth, true),
      height: scaleToContainer(defaultHeight, false),
      realX: defaultX,
      realY: defaultY,
      realWidth: defaultWidth,
      realHeight: defaultHeight,
    })
  }
}

const removeWidget = (widgetId) => {
  const index = arrangedWidgets.value.findIndex(w => w.id === widgetId)
  if (index > -1) {
    arrangedWidgets.value.splice(index, 1)
  }
}

const resetLayout = () => {
  arrangedWidgets.value = []
  loadLayout()
}

const clearAllWidgets = () => {
  ElMessageBox.confirm(
    'Are you sure you want to clear all widgets?',
    'Confirm',
    {
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      type: 'warning',
    },
  ).then(() => {
    arrangedWidgets.value = []
    ElMessage.success('All widgets cleared')
  }).catch(() => {})
}

// Event handlers for widget dragging and resizing
const onWidgetDragging = (widgetId, event) => {
  const widget = arrangedWidgets.value.find(w => w.id === widgetId)
  if (widget) {
    widget.x = event.x
    widget.y = event.y
    widget.realX = scaleToReal(event.x, true)
    widget.realY = scaleToReal(event.y, false)
  }
}

const onWidgetResizing = (widgetId, event) => {
  const widget = arrangedWidgets.value.find(w => w.id === widgetId)
  if (widget) {
    widget.width = event.width
    widget.height = event.height
    widget.realWidth = scaleToReal(event.width, true)
    widget.realHeight = scaleToReal(event.height, false)
  }
}

const onWidgetDragStop = (widgetId, event) => {
  onWidgetDragging(widgetId, event)
}

const onWidgetResizeStop = (widgetId, event) => {
  onWidgetResizing(widgetId, event)
}

// Dialog management
const close = () => {
  visible.value = false
}

const open = async () => {
  visible.value = true
  await nextTick()

  // Get primary display info
  try {
    const display = await window.electron?.ipcRenderer?.invoke('get-primary-display')
    if (display) {
      screenInfo.value = {
        width: display.workArea.width,
        height: display.workArea.height,
        scaleFactor: display.scaleFactor,
      }
    }
  }
  catch (error) {
    console.warn('Failed to get display info:', error)
  }

  // Update container dimensions
  await nextTick()
  if (arrangementAreaRef.value) {
    const rect = arrangementAreaRef.value.getBoundingClientRect()
    containerDimensions.value = {
      width: rect.width - 40, // Account for padding
      height: rect.height - 40,
    }
  }

  loadDevices()
  loadLayout()
}

// Save layout
const saveLayout = () => {
  if (arrangedWidgets.value.length === 0) {
    ElMessage.warning('No widgets to save')
    return
  }

  // 分别保存全局和设备配置
  arrangedWidgets.value.forEach((widget) => {
    const config = {
      '--window-width': Math.round(widget.realWidth).toString(),
      '--window-height': Math.round(widget.realHeight).toString(),
      '--window-x': Math.round(widget.realX).toString(),
      '--window-y': Math.round(widget.realY).toString(),
    }

    const scrcpy = window.appStore.get('scrcpy')

    if (widget.type === 'global') {
      // 保存全局配置
      const globalConfig = {
        ...scrcpy.global || {},
        ...config,
      }

      window.appStore.set('scrcpy.global', globalConfig)
    }
    else {
      // 保存设备特定配置
      const deviceConfig = {
        ...scrcpy[widget.deviceId] || {},
        ...config,
      }

      window.appStore.set(['scrcpy', widget.deviceId], deviceConfig)
    }
  })

  // Update preference store
  preferenceStore.init()

  ElMessage.success(`Layout saved successfully for ${arrangedWidgets.value.length} widget(s)`)
  close()
}

const onClosed = () => {
  arrangedWidgets.value = []
}

// Expose methods
defineExpose({
  open,
  close,
})
</script>

<style lang="postcss" scoped>
.arrange-dialog {
  .el-dialog__body {
    padding: 20px;
    height: 70vh;
    overflow: hidden;
  }
}

.arrange-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.control-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.layout-info {
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.empty-hint {
  color: #999;
  font-size: 14px;
  font-style: italic;
}

.arrangement-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.screen-container {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.widget-window {
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.global-widget {
  border: 2px solid #409eff !important;
}

.device-widget {
  border: 2px solid #67c23a !important;
}

.widget-window:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.global-widget:hover {
  border-color: #66b1ff !important;
}

.device-widget:hover {
  border-color: #85ce61 !important;
}

.widget-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.widget-name {
  font-weight: 500;
  color: #333;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.remove-btn {
  width: 20px !important;
  height: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  font-size: 10px;
}

.widget-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-info {
  text-align: center;
  font-size: 10px;
  color: #666;
  line-height: 1.4;
}

.widget-info p {
  margin: 2px 0;
}

/* Custom handle styles for better visibility */
:deep(.handle) {
  background: #409eff !important;
  border: 2px solid white !important;
  border-radius: 50%;
  width: 12px !important;
  height: 12px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.handle:hover) {
  background: #66b1ff !important;
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    align-items: stretch;
  }

  .control-section {
    justify-content: center;
  }

  .arrangement-area {
    padding: 10px;
  }
}
</style>
