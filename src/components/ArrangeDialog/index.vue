<template>
  <el-dialog
    v-model="visible"
    title="Window Arrangement"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    append-to-body
    fullscreen
    class="el-dialog--beautify el-dialog--flex el-dialog--fullscreen"
    @closed="onClosed"
  >
    <div class="arrange-container overflow-auto">
      <!-- Control Panel -->
      <div class="control-panel">
        <el-dropdown type="primary" @command="addWidget">
          <el-button type="primary">
            <template #icon>
              <el-icon><el-icon-plus /></el-icon>
            </template>
            Add Widget
            <el-icon class="el-icon--right">
              <el-icon-arrow-down />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="global" :disabled="hasGlobalWidget">
                <el-icon><Setting /></el-icon>
                Global Configuration
              </el-dropdown-item>
              <el-dropdown-item
                v-for="device in availableDevices"
                :key="device.id"
                :command="device.id"
              >
                <el-icon><Monitor /></el-icon>
                {{ device.name || device.id }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button type="default" :icon="Refresh" @click="resetLayout">
          Reset Layout
        </el-button>

        <el-button
          type="danger"
          :icon="Delete"
          :disabled="arrangedWidgets.length === 0"
          @click="clearAllWidgets"
        >
          Clear All
        </el-button>
      </div>

      <!-- Arrangement Area -->
      <div ref="arrangementAreaRef" class="arrangement-area">
        <div
          ref="screenContainerRef"
          class="screen-container"
          :style="screenContainerStyle"
        >
          <VueDraggableResizable
            v-for="widget in arrangedWidgets"
            :key="widget.id"
            :x="widget.x"
            :y="widget.y"
            :w="widget.width"
            :h="widget.height"
            :min-width="50"
            :min-height="50"
            :parent="true"
            :grid="[5, 5]"
            class="widget-window" :class="[`${widget.type}-widget`]"
            @dragging="(x, y) => onWidgetDragging(widget.id, { x, y })"
            @resizing="(x, y, w, h) => onWidgetResizing(widget.id, { x, y, width: w, height: h })"
            @drag-stop="(x, y) => onWidgetDragStop(widget.id, { x, y })"
            @resize-stop="(x, y, w, h) => onWidgetResizeStop(widget.id, { x, y, width: w, height: h })"
          >
            <div class="widget-content">
              <header class="widget-header">
                <div class="widget-title">
                  <el-icon>
                    <Setting v-if="widget.type === 'global'" />
                    <Monitor v-else />
                  </el-icon>
                  <span class="widget-name">
                    {{ widget.type === 'global' ? 'Global' : (widget.name || widget.id) }}
                  </span>
                </div>
                <el-button
                  type="danger"
                  :icon="Close"
                  size="small"
                  circle
                  class="remove-btn"
                  @click="removeWidget(widget.id)"
                />
              </header>
              <main class="widget-body">
                <div class="widget-info">
                  <p>Size: {{ Math.round(widget.realWidth) }}×{{ Math.round(widget.realHeight) }}</p>
                  <p>Position: {{ Math.round(widget.realX) }}, {{ Math.round(widget.realY) }}</p>
                </div>
              </main>
            </div>
          </VueDraggableResizable>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="close">
        Cancel
      </el-button>
      <el-button type="primary" @click="saveLayout">
        Save Layout
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, nextTick, ref } from 'vue'
import { Close, Delete, Monitor, Refresh, Setting } from '@element-plus/icons-vue'
import VueDraggableResizable from 'vue-draggable-resizable'
import 'vue-draggable-resizable/style.css'
import { usePreferenceStore } from '$/store/preference/index.js'
import { useScaleScreen } from '$/hooks/useScaleScreen/index.js'

/**
 * ArrangeDialog - Window arrangement dialog component
 *
 * Features:
 * - Unified scaling logic using useScaleScreen hook
 * - Modular business logic with composables
 * - Optimized DOM structure and responsive design
 * - Clean separation of concerns
 */

const preferenceStore = usePreferenceStore()

// Dialog state
const visible = ref(false)
const arrangementAreaRef = ref(null)
const screenContainerRef = ref(null)

// Initialize scale screen hook
const {
  scaleConverter,
  containerWidth,
  containerHeight,
  getPrimaryDisplay,
} = useScaleScreen({
  containerRef: arrangementAreaRef,
})

// Widget management
const allDevices = ref([])
const arrangedWidgets = ref([])

// Computed properties
const hasGlobalWidget = computed(() => {
  return arrangedWidgets.value.some(w => w.type === 'global')
})

const availableDevices = computed(() => {
  const arrangedDeviceIds = arrangedWidgets.value
    .filter(w => w.type === 'device')
    .map(w => w.deviceId)

  return allDevices.value.filter(d => !arrangedDeviceIds.includes(d.id))
})

const screenContainerStyle = computed(() => {
  return {
    width: `${containerWidth.value}px`,
    height: `${containerHeight.value}px`,
    backgroundColor: '#f5f5f5',
    border: '2px solid #ddd',
    position: 'relative',
    margin: '0 auto',
  }
})

// Device management composable
const useDeviceManagement = () => {
  const loadDevices = () => {
    const devices = window.appStore.get('device') || {}
    allDevices.value = Object.values(devices).map(device => ({
      ...device,
      name: device.name || device.model?.split(':')[1] || device.id,
    }))
  }

  return { loadDevices }
}

const { loadDevices } = useDeviceManagement()

// Layout management composable
const useLayoutManagement = () => {
  const createWidgetFromConfig = (config, widgetData) => {
    const realWidth = Number.parseInt(config['--window-width']) || 300
    const realHeight = Number.parseInt(config['--window-height']) || 600
    const realX = Number.parseInt(config['--window-x']) || 0
    const realY = Number.parseInt(config['--window-y']) || 0

    const containerRect = scaleConverter({ width: realWidth, height: realHeight, x: realX, y: realY })

    return {
      ...widgetData,
      x: containerRect.x,
      y: containerRect.y,
      width: containerRect.width,
      height: containerRect.height,
      realX,
      realY,
      realWidth,
      realHeight,
    }
  }

  const loadLayout = () => {
    arrangedWidgets.value = []
    const scrcpy = window.appStore.get('scrcpy')

    // Load global widget
    const globalConfig = scrcpy.global || {}
    if (globalConfig['--window-width'] && globalConfig['--window-height']) {
      const widget = createWidgetFromConfig(globalConfig, {
        id: 'global',
        type: 'global',
        name: 'Global',
      })
      console.log('widget', widget)
      arrangedWidgets.value.push(widget)
    }

    // Load device widgets
    allDevices.value.forEach((device) => {
      const deviceConfig = scrcpy[device.id] || {}
      if (deviceConfig['--window-width'] && deviceConfig['--window-height']) {
        const widget = createWidgetFromConfig(deviceConfig, {
          id: device.id,
          type: 'device',
          deviceId: device.id,
          name: device.name || device.model?.split(':')[1] || device.id,
        })
        arrangedWidgets.value.push(widget)
      }
    })
  }

  return { loadLayout, createWidgetFromConfig }
}

const { loadLayout, createWidgetFromConfig } = useLayoutManagement()

// Widget management composable
const useWidgetManagement = () => {
  const addWidget = (command) => {
    if (command === 'global') {
      if (hasGlobalWidget.value) {
        ElMessage.warning('Global widget already exists')
        return
      }

      const globalConfig = window.appStore.get('scrcpy.global') || {}
      const config = {
        '--window-width': globalConfig['--window-width'] || '300',
        '--window-height': globalConfig['--window-height'] || '600',
        '--window-x': globalConfig['--window-x'] || (arrangedWidgets.value.length * 50).toString(),
        '--window-y': globalConfig['--window-y'] || (arrangedWidgets.value.length * 50).toString(),
      }

      const widget = createWidgetFromConfig(config, {
        id: 'global',
        type: 'global',
        name: 'Global',
      })
      arrangedWidgets.value.push(widget)
    }
    else {
      const device = allDevices.value.find(d => d.id === command)
      if (!device) {
        ElMessage.error('Device not found')
        return
      }

      const deviceConfig = window.appStore.get(`scrcpy.${command}`) || {}
      const globalConfig = window.appStore.get('scrcpy.global') || {}
      const config = {
        ...globalConfig,
        ...deviceConfig,
        '--window-width': deviceConfig['--window-width'] || globalConfig['--window-width'] || '300',
        '--window-height': deviceConfig['--window-height'] || globalConfig['--window-height'] || '600',
        '--window-x': deviceConfig['--window-x'] || globalConfig['--window-x'] || (arrangedWidgets.value.length * 50).toString(),
        '--window-y': deviceConfig['--window-y'] || globalConfig['--window-y'] || (arrangedWidgets.value.length * 50).toString(),
      }

      const widget = createWidgetFromConfig(config, {
        id: device.id,
        type: 'device',
        deviceId: device.id,
        name: device.name || device.model?.split(':')[1] || device.id,
      })
      arrangedWidgets.value.push(widget)
    }
  }

  const removeWidget = (widgetId) => {
    const index = arrangedWidgets.value.findIndex(w => w.id === widgetId)
    if (index > -1) {
      arrangedWidgets.value.splice(index, 1)
    }
  }

  const clearAllWidgets = () => {
    ElMessageBox.confirm(
      'Are you sure you want to clear all widgets?',
      'Confirm',
      {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning',
      },
    ).then(() => {
      arrangedWidgets.value = []
      ElMessage.success('All widgets cleared')
    }).catch(() => {})
  }

  return { addWidget, removeWidget, clearAllWidgets }
}

const { addWidget, removeWidget, clearAllWidgets } = useWidgetManagement()

const resetLayout = () => {
  arrangedWidgets.value = []
  loadLayout()
}

// Widget event handlers composable
const useWidgetEvents = () => {
  const updateWidgetPosition = (widget, containerPos) => {
    const realPos = scaleConverter(containerPos, true)
    widget.x = containerPos.x
    widget.y = containerPos.y
    widget.realX = realPos.x
    widget.realY = realPos.y
  }

  const updateWidgetSize = (widget, containerSize) => {
    const realSize = scaleConverter(containerSize, true)
    widget.width = containerSize.width
    widget.height = containerSize.height
    widget.realWidth = realSize.width
    widget.realHeight = realSize.height
  }

  const onWidgetDragging = (widgetId, event) => {
    const widget = arrangedWidgets.value.find(w => w.id === widgetId)
    if (widget) {
      updateWidgetPosition(widget, { x: event.x, y: event.y })
    }
  }

  const onWidgetResizing = (widgetId, event) => {
    const widget = arrangedWidgets.value.find(w => w.id === widgetId)
    if (widget) {
      updateWidgetPosition(widget, { x: event.x, y: event.y })
      updateWidgetSize(widget, { width: event.width, height: event.height })
    }
  }

  const onWidgetDragStop = (widgetId, event) => {
    onWidgetDragging(widgetId, event)
  }

  const onWidgetResizeStop = (widgetId, event) => {
    onWidgetResizing(widgetId, event)
  }

  return {
    onWidgetDragging,
    onWidgetResizing,
    onWidgetDragStop,
    onWidgetResizeStop,
  }
}

const {
  onWidgetDragging,
  onWidgetResizing,
  onWidgetDragStop,
  onWidgetResizeStop,
} = useWidgetEvents()

// Dialog management composable
const useDialogManagement = () => {
  const close = () => {
    visible.value = false
  }

  const open = async () => {
    try {
      visible.value = true
      await nextTick()

      // Initialize display info using the hook
      await getPrimaryDisplay()

      loadDevices()
      loadLayout()
    }
    catch (error) {
      console.error('Failed to open arrange dialog:', error)
      ElMessage.error('Failed to initialize dialog')
    }
  }

  const onClosed = () => {
    arrangedWidgets.value = []
  }

  return { open, close, onClosed }
}

const { open, close, onClosed } = useDialogManagement()

// Save layout composable
const useSaveLayout = () => {
  const saveLayout = () => {
    if (arrangedWidgets.value.length === 0) {
      ElMessage.warning('No widgets to save')
      return
    }

    arrangedWidgets.value.forEach((widget) => {
      const config = {
        '--window-width': Math.round(widget.realWidth).toString(),
        '--window-height': Math.round(widget.realHeight).toString(),
        '--window-x': Math.round(widget.realX).toString(),
        '--window-y': Math.round(widget.realY).toString(),
      }

      const scrcpy = window.appStore.get('scrcpy')

      if (widget.type === 'global') {
        const globalConfig = { ...scrcpy.global || {}, ...config }
        window.appStore.set('scrcpy.global', globalConfig)
      }
      else {
        const deviceConfig = { ...scrcpy[widget.deviceId] || {}, ...config }
        window.appStore.set(['scrcpy', widget.deviceId], deviceConfig)
      }
    })

    preferenceStore.init()
    ElMessage.success(`Layout saved successfully for ${arrangedWidgets.value.length} widget(s)`)
    close()
  }

  return { saveLayout }
}

const { saveLayout } = useSaveLayout()

// Expose public methods for parent components
defineExpose({
  open,
  close,
})
</script>

<style lang="postcss" scoped>
.arrange-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.arrangement-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.screen-container {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.widget-window {
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 2px solid transparent;

  &.global-widget {
    border-color: #409eff;

    &:hover {
      border-color: #66b1ff;
    }
  }

  &.device-widget {
    border-color: #67c23a;

    &:hover {
      border-color: #85ce61;
    }
  }

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.widget-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.widget-name {
  font-weight: 500;
  color: #333;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.remove-btn {
  width: 20px !important;
  height: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  font-size: 10px;
}

.widget-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-info {
  text-align: center;
  font-size: 10px;
  color: #666;
  line-height: 1.4;

  p {
    margin: 2px 0;
  }
}

/* Custom drag handle styles */
:deep(.handle) {
  background: #409eff !important;
  border: 2px solid white !important;
  border-radius: 50%;
  width: 12px !important;
  height: 12px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;

  &:hover {
    background: #66b1ff !important;
    transform: scale(1.1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 15px;
  }

  .arrangement-area {
    padding: 10px;
    min-height: 300px;
  }

  .widget-name {
    max-width: 80px;
  }
}
</style>
