:root {
  --el-color-primary: rgba(var(--color-primary), 1);
  --el-color-primary-light-3: rgba(var(--color-primary-400), 1);
  --el-color-primary-light-5: rgba(var(--color-primary-300), 1);
  --el-color-primary-light-7: rgba(var(--color-primary-200), 1);
  --el-color-primary-light-8: rgba(var(--color-primary-100), 1);
  --el-color-primary-light-9: rgba(var(--color-primary-50), 1);
  --el-color-primary-dark-2: rgba(var(--color-primary-600), 1);
}

:root.dark {
  --el-color-primary: rgba(var(--color-primary-500), 1);
  --el-color-primary-light-3: rgba(var(--color-primary-300), 1);
  --el-color-primary-light-5: rgba(var(--color-primary-500), 1);
  --el-color-primary-light-7: rgba(var(--color-primary-700), 1);
  --el-color-primary-light-8: rgba(var(--color-primary-800), 1);
  --el-color-primary-light-9: rgba(var(--color-primary-900), 1);
  --el-color-primary-dark-2: rgba(var(--color-primary-400), 1);
}

.el-dialog--flex {
  height: calc(100% - 20vh - 8px) !important;
  @apply flex flex-col !my-[10vh];
  .el-dialog__header,
  .el-dialog__footer {
    @apply flex-none;
  }
  .el-dialog__body {
    @apply flex-1 !h-0;
  }

  &.is-fullscreen {
    @apply !my-0 !h-full;
  }
}


.el-dialog--headless {
  .el-dialog__header,
  .el-dialog__footer {
    @apply !hidden;
  }

  .el-dialog__body {
    @apply !p-0;
  }
}

.el-dialog--beautify {
  @apply !rounded-lg;

  .el-dialog__title {
    @apply relative;

    &::before {
      content: '';
      @apply absolute inset-x-0 bottom-0 h-2 bg-primary-500/30;
    }
  }

  .el-dialog__footer {
    @apply min-h-8;
  }
}
