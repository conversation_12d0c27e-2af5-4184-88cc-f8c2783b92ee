{"common.yes": "はい", "common.no": "いいえ", "common.cancel": "キャンセル", "common.confirm": "確認", "common.restart": "再起動", "common.default": "既定", "common.tips": "ヒント", "common.open": "開く", "common.input.placeholder": "入力してください", "common.success": "操作が成功しました", "common.success.batch": "一括操作が成功しました", "common.fail": "操作に失敗しました", "common.starting": "開始中", "common.loading": "読込中", "common.search": "検索", "common.batch": "一括", "common.device": "デバイス", "common.progress": "進行中", "common.finished": "完了済み", "common.stop": "停止", "common.remove": "削除", "common.select.please": "選択してください", "common.required": "このフィールドは空欄にできません", "common.download": "ダウンロード", "common.downloading": "ダウンロード中", "common.delete": "削除", "common.name": "名前", "common.size": "サイズ", "common.warning": "警告", "common.info": "メッセージ", "common.danger": "エラー", "common.connecting": "接続中", "common.language.name": "言語", "common.language.placeholder": "言語を選択してください", "time.update": "更新時間", "time.unit.month": "ヶ月", "time.unit.week": "週間", "time.unit.day": "日", "time.unit.hour": "時間", "time.unit.minute": "分", "time.unit.second": "秒", "time.unit.millisecond": "ミリ秒", "appClose.name": "メインパネルを閉じる", "appClose.question": "毎回確認する", "appClose.minimize": "トレイに最小化", "appClose.quit": "終了", "appClose.quit.cancel": "キャンセル", "appClose.quit.loading": "サービスを停止中...", "appClose.message": "本当に終了してもよろしいですか？", "appClose.remember": "選択を記憶しますか？", "dependencies.lack.title": "お知らせ", "dependencies.lack.content": "このソフトウェアは {name} に依存しています。指定された依存関係が正しくインストールされていることを確認するか、設定で依存関係の場所を手動で設定してください。", "device.list": "デバイス", "device.list.empty": "デバイスがありません", "device.serial": "シリアル", "device.name": "名前", "device.remark": "リマーク", "device.permission.error": "デバイスの権限エラーです。デバイスを再接続して USB デバッグを許可してください。", "device.terminal.name": "ターミナル", "device.status": "ステータス", "device.status.connected": "接続済み", "device.status.offline": "オフライン", "device.status.unauthorized": "未承認", "device.status.authorizing": "承認中", "device.battery": "デバイスのバッテリー", "device.isCharging": "充電ステータス", "device.temperature": "デバイスの温度", "device.powerSource": "電源ソース", "device.voltage": "デバイスの電圧", "device.task.name": "スケジュールされたタスク", "device.task.tips": " 注意: コンピューターが起動状態であることを確認してください。起動状態でないとスケジュールされたタスクが正しく実行されません。", "device.task.list": "タスクの一覧", "device.task.type": "タスクタイプ", "device.task.frequency": "実行頻度", "device.task.frequency.timeout": "単一で実行", "device.task.frequency.interval": "周期的に繰り返し", "device.task.timeout": "実行時間", "device.task.timeout.tips": "現在の時刻より前には設定できません。", "device.task.timeout.expired": "タスクの有効期限が切れました", "device.task.interval": "繰り返しの間隔", "device.task.devices": "関連するデバイス", "device.task.noRepeat": "繰り返さない", "device.task.restart": "再度実行", "device.task.extra.app": "アプリを選択", "device.task.extra.shell": "スクリプトを選択", "device.wireless.name": "ワイヤレス", "device.wireless.mode": "ワイヤレス", "device.wireless.mode.error": "ローカルエリアネットワーク接続アドレスを取得できない場合は、ネットワークを確認してください", "device.wireless.connect.qr": "QR コード接続", "device.wireless.connect.qr.pairing": "ペアリング", "device.wireless.connect.qr.connecting": "接続中", "device.wireless.connect.qr.connecting-fallback": "接続中", "device.wireless.connect.qr.connected": "接続に成功しました", "device.wireless.connect.qr.error": "QR コード接続", "device.wireless.connect.name": "接続", "device.wireless.connect.error.title": "接続に失敗しました", "device.wireless.connect.error.detail": "エラーの詳細", "device.wireless.connect.error.reasons[0]": "考えられる理由:", "device.wireless.connect.error.reasons[1]": "IP またはポートが正しくない", "device.wireless.connect.error.reasons[2]": "デバイスをペアリングしていない", "device.wireless.connect.error.reasons[3]": "IP が同じサブネットにない", "device.wireless.connect.error.reasons[4]": "adb パスのエラー", "device.wireless.connect.error.reasons[5]": "その他の不明なエラー", "device.wireless.connect.error.confirm": "ワイヤレスペア", "device.wireless.connect.error.cancel": "@:common.cancel", "device.wireless.connect.error.no-address": "ワイヤレスデバッグアドレスは空欄にできません", "device.wireless.connect.success": "接続に成功しました", "device.wireless.connect.batch.name": "過去に接続したすべてのデバイスを接続する", "device.wireless.disconnect.start": "切断", "device.wireless.disconnect.progress": "切断中", "device.wireless.disconnect.success": "切断済み", "device.wireless.pair": "ワイヤレスペア", "device.wireless.pair.tips": "開発者向けオプション -> ワイヤレスデバッグ -> デバイスのペアリング から次の情報を取得します。", "device.wireless.pair.address": "ペア IP アドレス", "device.wireless.pair.address.message": "ペアアドレスは空欄にできません", "device.wireless.pair.address.placeholder": "ペア IP アドレスを入力", "device.wireless.pair.port": "ペアポート", "device.wireless.pair.port.message": "ペアポートは空欄にできません", "device.wireless.pair.port.placeholder": "ペアポートを入力", "device.wireless.pair.code": "ペアコード", "device.wireless.pair.code.message": "ペアコードは空欄にできません", "device.wireless.pair.code.placeholder": "ペアコードを入力", "device.reset.title": "操作に失敗しました", "device.reset.reasons[0]": "これは互換性のない設定が原因である可能性があります。設定をリセットしますか？", "device.reset.reasons[1]": "注意: リセットすると以前に保存した設定はすべて消去されるため、リセットを行う前に設定をバックアップすることを推奨します。", "device.reset.confirm": "設定をリセット", "device.reset.cancel": "@:common.cancel", "device.reset.success": "成功しました、再度お試しください", "device.refresh.name": "更新", "device.restart.name": "再起動", "device.log.name": "ログ", "device.mirror.start": "接続", "device.record.progress": "録画中", "device.record.success.title": "録画が成功しました", "device.record.success.message": "録画の保存先を開きますか？", "device.actions.more.name": "エクストラ", "device.actions.more.record.name": "録画を開始", "device.actions.more.camera.name": "カメラを起動", "device.actions.more.recordCamera.name": "カメラを録画", "device.actions.more.recordAudio.name": "オーディオを録音", "device.actions.more.otg.name": "OTG を開始", "device.actions.more.custom.name": "開始をカスタマイズ", "device.control.name": "コントロール", "device.control.more": "その他のコントロール", "device.control.install": "アプリをインストール", "device.control.install.placeholder": "インストールするアプリを選択", "device.control.install.progress": "{deviceName} にアプリをインストール中です...", "device.control.install.success": "{totalCount} 個のアプリが {deviceName} にインストールされました。{successCount} 個が成功、{failCount} 個が失敗しました", "device.control.install.success.single": "{deviceName} にアプリをインストールしました", "device.control.install.error": "インストールに失敗しました。アプリを確認して再度お試しください。", "device.control.file.name": "ファイルマネージャー", "device.control.file.push": "ファイルを転送", "device.control.file.push.placeholder": "転送するファイルを選択してください", "device.control.file.push.loading": "ファイルを転送", "device.control.file.push.success.name": "ファイルを正常に転送しました", "device.control.file.push.success": "{totalCount} 個のファイルを {deviceName} に転送しました。{successCount} 個が成功、{failCount} 個が失敗しました。", "device.control.file.push.success.single": "ファイルは {deviceName} に転送されました", "device.control.file.push.error": "ファイルの転送に失敗しました。ファイルを確認して再度お試しください。", "device.control.file.manager.storage": "内部ストレージ", "device.control.file.manager.add": "新しいフォルダ", "device.control.file.manager.upload": "ファイルをアップロード", "device.control.file.manager.upload.directory": "ディレクトリをアップロード", "device.control.file.manager.download": "ファイルをダウンロード", "device.control.file.manager.download.tips": "選択したコンテンツをダウンロードしますか？", "device.control.file.manager.delete.tips": "選択したコンテンツを削除してもよろしいですか？", "device.control.terminal.command.name": "コマンドを実行", "device.control.terminal.script.name": "スクリプトを実行", "device.control.terminal.script.select": "実行するスクリプトを選択してください", "device.control.terminal.script.push.loading": "スクリプトを転送...", "device.control.terminal.script.push.success": "スクリプトの転送に成功しました", "device.control.terminal.script.enter": "スクリプトの実行を確認するには、Enter キーを入力してください", "device.control.terminal.script.success": "スクリプトの実行に成功しました", "device.control.capture": "スクリーンショット", "device.control.capture.progress": "{deviceName} のスクリーンショットをキャプチャ中...", "device.control.capture.success.message": "スクリーンショットの場所を開きますか？", "device.control.capture.success.message.title": "スクリーンショットが成功しました", "device.control.reboot": "再起動", "device.control.turnScreenOff": "画面を OFF にする", "device.control.turnScreenOff.tips": "コントロールを維持しながら画面を OFF にします (試験的): このアクションにより、EscrcpyHelper プロセスが作成されます。このプロセスを手動で閉じると、画面が再び開きます。", "device.control.startApp": "アプリを開始", "device.control.startApp.useMainScreen": "メイン画面を使用して開く", "device.control.power": "電源", "device.control.power.tips": "画面の ON/OFF を切り替え", "device.control.notification": "通知", "device.control.notification.tips": "通知パネルを開く", "device.control.return": "戻る", "device.control.home": "ホーム", "device.control.switch": "切り替え", "device.control.gnirehtet": "Gnirehtet", "device.control.gnirehtet.tips": "Gnirehtet で Android 用のリバーステザリングを提供します。注意: 最初の接続時にデバイスでの承認が必要です。", "device.control.gnirehtet.start": "サービスを開始", "device.control.gnirehtet.start.success": "Gnirehtet リバーステザリング機能が正常に開始されました", "device.control.gnirehtet.stop": "サービスを停止", "device.control.gnirehtet.stop.success": "サービスは正常に停止しました", "device.control.gnirehtet.running": "サービスを実行中", "device.control.gnirehtet.stopping": "サービスを停止中", "device.control.mirror-group.name": "ミラーグループ", "device.control.mirror-group.tips": "有効化すると複数のシミュレートされたセカンダリディスプレイをミラーリングし、ミラーリングされた各ウィンドウを操作することでマルチスクリーンコラボレーションを実現できます。ただし、ROM が対応していることとデスクトップモードの有効化が必要です。", "device.control.mirror-group.open": "{num} 個のウィンドウを開く", "device.control.mirror-group.close": "補助ディスプレイのデバイスを閉じる", "device.control.mirror-group.appClose.tips": "特定のデバイスがすべてのコントロールウィンドウを終了後に自動終了が失敗する問題を解決するために使用されます。", "device.control.volume.name": "音量", "device.control.volume-up.name": "音量を上げる", "device.control.volume-down.name": "音量を下げる", "device.control.volume-mute.name": "消音", "device.control.rotation.name": "画面回転", "device.control.rotation.vertically": "垂直", "device.control.rotation.horizontally": "水平", "device.control.rotation.auto": "自動", "device.control.rotation.disable": "無効", "preferences.name": "設定", "preferences.reset": "既定に戻す", "preferences.scope.global": "グローバル", "preferences.scope.placeholder": "スコープの設定", "preferences.scope.no-data": "データなし", "preferences.scope.details[0]": "グローバルまたはデバイスごとの設定を行う", "preferences.scope.details[1]": "グローバル: すべてのデバイスに適用", "preferences.scope.details[2]": "デバイスごと: 1 つのデバイスのグローバル設定を上書きする", "preferences.config.import.name": "インポート", "preferences.config.import.placeholder": "構成ファイルを選択", "preferences.config.import.success": "インポートに成功しました", "preferences.config.export.name": "エクスポート", "preferences.config.export.message": "構成をエクスポート", "preferences.config.export.placeholder": "エクスポート先を選択", "preferences.config.export.success": "エクスポートに成功しました", "preferences.config.edit.name": "編集", "preferences.config.reset.name": "リセット", "preferences.common.name": "一般", "preferences.common.theme.name": "テーマ", "preferences.common.theme.placeholder": "テーマを設定", "preferences.common.theme.options[0]": "ライトモード", "preferences.common.theme.options[1]": "ダークモード", "preferences.common.theme.options[2]": "システムに従う", "preferences.common.debug.name": "デバッグ", "preferences.common.debug.placeholder": "デバッグモードを有効化", "preferences.common.debug.tips": "ログにデバッグ情報を表示します。パフォーマンス向上のために無効化してください。有効化するには再起動してください。", "preferences.common.file.name": "ファイルの場所", "preferences.common.file.placeholder": "ユーザーのデスクトップ", "preferences.common.file.tips": "スクリーンショットと録画の保存先です。", "preferences.common.adb.name": "ADB のパス", "preferences.common.adb.placeholder": "カスタム ADB のパス", "preferences.common.adb.tips": "デバイスを接続するための ADB のパスです。", "preferences.common.scrcpy.name": "Scrcpy のパス", "preferences.common.scrcpy.placeholder": "カスタム Scrcpy のパス", "preferences.common.scrcpy.tips": "コントロールデバイスへの Scrcpy のパスです。", "preferences.common.scrcpy.append.name": "Scrcpy の引数", "preferences.common.scrcpy.append.placeholder": "Scrcpy コマンドに引数を追加", "preferences.common.scrcpy.append.tips": "注意: 入力された引数は、重複する引数をフィルタリングせずに Scrcpy コマンドに直接追加されます。", "preferences.common.gnirehtet.name": "Gnirehtet のパス", "preferences.common.gnirehtet.placeholder": "カスタム Gnirehtet のパス", "preferences.common.gnirehtet.tips": "デバイスのリバーステザリングを行うために使用する Gnirehtet のパスです。", "preferences.common.gnirehtet.fix.name": "Gnirehtet の修正", "preferences.common.gnirehtet.fix.placeholder": "有効化すると Gnirehtet.apk のインストールチェックが無効になり、一部のデバイスでの接続の問題が解決する可能性があります。", "preferences.common.gnirehtet.fix.tips": "注意: これにより起動するたびに Gnirehtet.apk が再インストールされる可能性があります。", "preferences.common.gnirehtet.append.name": "Gnirehtet の引数", "preferences.common.gnirehtet.append.placeholder": "Gnirehtet コマンドに引数を追加", "preferences.common.gnirehtet.append.tips": "注意: 入力された引数は、重複する引数をフィルタリングせずに Gnirehtet コマンドに直接追加されます。", "preferences.common.floatControl.name": "フローティングコントロールバー", "preferences.common.floatControl.placeholder": "有効化するとミラーリング中にデバイスのフローティングコントロールバーを自動で開きます", "preferences.common.auto-connect.name": "自動接続", "preferences.common.auto-connect.placeholder": "有効化するとソフトウェアの起動時に過去のデバイスの自動接続を行います。", "preferences.common.auto-mirror.name": "自動ミラー", "preferences.common.auto-mirror.placeholder": "有効化するとデバイスの一覧内のデバイスでのミラーの実行を行います。", "preferences.common.edgeHidden.name": "メインパネルを自動で非表示", "preferences.common.edgeHidden.placeholder": "有効化するとマウスを画面の端に近づけることで自動的にパネルが非表示なります", "preferences.common.edgeHidden.tips": "注意: 変更を適用するにはアプリの再起動が必要です。", "preferences.common.imeFix.name": "アプリキーボードの問題を修正", "preferences.common.imeFix.placeholder": "有効化するとアプリの起動時に現在のミラーウィンドウ上に入力方法が表示されない問題を解決します。", "preferences.common.imeFix.tips": "注意: この機能は Scrcpy v3.2 以降でのみサポートされています。それ以前のバージョンで使用するとエラーが発生します。", "preferences.video.name": "動画", "preferences.video.disable-video.name": "動画の転送を無効化する", "preferences.video.disable-video.placeholder": "有効化すると動画の転送が無効になります", "preferences.video.video-source.name": "動画のソース", "preferences.video.video-source.placeholder": "デバイスの画面", "preferences.video.video-source.display": "画面", "preferences.video.video-source.camera": "カメラ", "preferences.video.resolution.name": "最大サイズ", "preferences.video.resolution.placeholder": "デバイスサイズ、形式: 1080", "preferences.video.bit.name": "動画のビットレート", "preferences.video.bit.placeholder": "8000000、形式: 8M、8000000", "preferences.video.video-code.name": "ビデオコーデック", "preferences.video.video-code.placeholder": "H.264", "preferences.video.refresh-rate.name": "フレームレート", "preferences.video.refresh-rate.placeholder": "60", "preferences.video.display-orientation.name": "画面の方向", "preferences.video.display-orientation.placeholder": "デバイスの画面の方向", "preferences.video.angle.name": "画面の角度", "preferences.video.angle.placeholder": "回転なし、形式: 15", "preferences.video.angle.tips": "注意: このオプションは録音中にも有効です。", "preferences.video.screen-cropping.name": "クロップ", "preferences.video.screen-cropping.placeholder": "クロップなし、形式: 1224:1440:0:0", "preferences.video.display.name": "画面", "preferences.video.display.placeholder": "メインディスプレイ", "preferences.video.video-buffer.name": "ビデオバッファ", "preferences.video.video-buffer.placeholder": "0", "preferences.video.receiver-buffer.name": "レシーバーバッファ (v412)", "preferences.video.receiver-buffer.placeholder": "0", "preferences.device.name": "デバイス", "preferences.device.show-touch.name": "タッチを表示", "preferences.device.show-touch.placeholder": "タッチフィードバックのドットを有効化します", "preferences.device.show-touch.tips": "物理デバイスのみです。", "preferences.device.stay-awake.name": "画面を常に ON にする", "preferences.device.stay-awake.placeholder": "デバイスのスリープを防止します", "preferences.device.stay-awake.tips": "有線接続時のみです。", "preferences.device.turnScreenOff.name": "画面を OFF に切り替え", "preferences.device.turnScreenOff.placeholder": "操作時にデバイスの画面を OFF にする", "preferences.device.screenOffTimeout.name": "画面のタイムアウト", "preferences.device.screenOffTimeout.placeholder": "デバイスの既定", "preferences.device.screenOffTimeout.tips": "画面のタイムアウト設定を変更して、終了時にデバイスの既定に復元します。", "preferences.device.control-end-video.name": "終了画面で OFF にする", "preferences.device.control-end-video.placeholder": "コントロールの終了後に画面を OFF にします", "preferences.device.control-in-stop-charging.name": "充電を停止", "preferences.device.control-in-stop-charging.placeholder": "コントロール中に充電を停止します", "preferences.device.control-in-stop-charging.tips": "コントロール中に充電を停止します。", "preferences.device.display-overlay.name": "シミュレーションディスプレイ", "preferences.device.display-overlay.placeholder": "デバイスのサイズ、形式: 1920x1080/420, 1920x1080, /240", "preferences.device.display-overlay.tips": "シミュレートされた補助ディスプレイのサイズと解像度を調整するために使用されます。アプリの起動とマルチスクリーンコラボレーション (ミラーリンググループ) はこのオプションに依存します。", "preferences.window.name": "ウィンドウ", "preferences.window.borderless.name": "ボーダーレス", "preferences.window.borderless.placeholder": "ボーダーレスコントロールウィンドウ", "preferences.window.full-screen.name": "フルスクリーン", "preferences.window.full-screen.placeholder": "フルスクリーンコントロールウィンドウ", "preferences.window.always-top.name": "常に手前に表示", "preferences.window.always-top.placeholder": "コントロールウィンドウを常に手前に表示します", "preferences.window.disable-screen-saver.name": "スクリーンセーバーを無効化", "preferences.window.disable-screen-saver.placeholder": "コンピューターのスクリーンセーバーを無効化します", "preferences.window.size.width": "ウィンドウの幅", "preferences.window.size.width.placeholder": "デバイスの幅", "preferences.window.size.width.tips": "注意: この設定を変更すると表示がぼやける可能性があります。", "preferences.window.size.height": "ウィンドウの高さ", "preferences.window.size.height.placeholder": "デバイスの高さ", "preferences.window.size.height.tips": "注意: この設定を変更すると表示がぼやける可能性があります。", "preferences.window.position.x": "ウィンドウの X 位置", "preferences.window.position.x.placeholder": "デスクトップの中央を基準", "preferences.window.position.y": "ウィンドウの Y 位置", "preferences.window.position.y.placeholder": "デスクトップの中央を基準", "preferences.record.name": "録画", "preferences.record.format.name": "形式", "preferences.record.format.placeholder": "mp4", "preferences.record.format.audio.name": "オーディオの形式", "preferences.record.format.audio.placeholder": "opus", "preferences.record.time-limit.name": "録音時間の制限", "preferences.record.time-limit.placeholder": "時間制限なし", "preferences.record.orientation.name": "動画の画面方向", "preferences.record.orientation.placeholder": "デバイスの画面方向", "preferences.record.no-video-playback.name": "動画再生を無効化する", "preferences.record.no-video-playback.placeholder": "有効化すると録画中に動画再生を無効化します。", "preferences.record.no-video-playback.tips": "注意: 動画は録画されますが、再生は無効になります。", "preferences.record.no-audio-playback.name": "オーディオ再生を無効化する", "preferences.record.no-audio-playback.placeholder": "有効化すると録音中にオーディオ再生を無効化します", "preferences.record.no-audio-playback.tips": "注意: 音声は録音されますが、再生は無効になります。", "preferences.audio.name": "オーディオ", "preferences.audio.disable-audio.name": "オーディオ転送を無効化する", "preferences.audio.disable-audio.placeholder": "有効化するとオーディオ転送が無効になります。", "preferences.audio.disable-audio.tips": "デバイスのオーディオキャプチャが異常な場合、このオプションを開いてミラーを正常に行えるかどうかを確認できます。", "preferences.audio.audioDup.name": "デバイスのオーディオを保持", "preferences.audio.audioDup.placeholder": "有効化するとミラーリング中でもデバイス上でオーディオを再生し続けます。", "preferences.audio.audioDup.tips": "注意: このオプションは Android 13 以降が必要です。アプリはオプトアウトができます (その場合はキャプチャされません)。", "preferences.audio.audio-source.name": "オーディオソース", "preferences.audio.audio-source.placeholder": "デバイスオーディオ出力", "preferences.audio.audio-source.tips": "ヒント: ソースとして「マイク」を選択すると音声を録音できます。", "preferences.audio.audio-source.output": "オーディオ出力全体を転送してデバイスでの再生を無効化します", "preferences.audio.audio-source.mic": "マイクをキャプチャする", "preferences.audio.audio-source.playback": "オーディオ再生をキャプチャします (Android アプリはオプトアウトできるため、出力全体が必ずしもキャプチャされるわけではありません)", "preferences.audio.audio-source.mic-unprocessed": "マイクの未処理 (そのまま) の音をキャプチャ", "preferences.audio.audio-source.mic-camcorder": "可能であればカメラと同じ向きで動画録画用に調整されたマイクをキャプチャします", "preferences.audio.audio-source.mic-voice-recognition": "音声認識用に調整されたマイクをキャプチャします", "preferences.audio.audio-source.mic-voice-communication": "音声通信用に調整されたマイクをキャプチャします (例にするとエコーキャンセルや自動ゲイン制御が利用可能な場合はそちらを使用します)", "preferences.audio.audio-source.voice-call": "音声通話をキャプチャ", "preferences.audio.audio-source.voice-call-uplink": "音声通話のアップリンクのみをキャプチャします", "preferences.audio.audio-source.voice-call-downlink": "音声通話のダウンリンクのみをキャプチャします", "preferences.audio.audio-source.voice-performance": "ライブパフォーマンス (カラオケ) 用に処理することを目的としたオーディオをキャプチャします。マイクとデバイスの再生の両方が含まれます。", "preferences.audio.audio-code.name": "オーディオコーデック", "preferences.audio.audio-code.placeholder": "opus", "preferences.audio.audio-bit-rate.name": "オーディオビットレート", "preferences.audio.audio-bit-rate.placeholder": "128000、形式: 128K、128000", "preferences.audio.audio-bit-rate.tips": "注意: このオプションは RAW オーディオコーデックには適用されません。", "preferences.audio.audio-buffer.name": "オーディオバッファ", "preferences.audio.audio-buffer.placeholder": "0", "preferences.audio.audio-output-buffer.name": "オーディオ出力バッファ", "preferences.audio.audio-output-buffer.placeholder": "5", "preferences.input.name": "入力", "preferences.input.mouse.name": "マウスモード", "preferences.input.mouse.placeholder": "sdk", "preferences.input.mouse.tips": "マウス入力モードを設定します。", "preferences.input.mouse.options[0].placeholder": "既定", "preferences.input.mouse.options[1].placeholder": "デバイス上の UHID カーネルモジュールを使用して物理 HID マウスをシミュレート", "preferences.input.mouse.options[2].placeholder": "AOAv2 プロトコルを使用して物理 HID マウスをシミュレート", "preferences.input.mouse.options[3].placeholder": "マウス入力を無効にする", "preferences.input.mouse-bind.name": "マウスバインド", "preferences.input.mouse-bind.tips": "このオプションは、マウスボタンの機能をカスタマイズします。4 文字のシーケンスを 2 セット使用して、プライマリとセカンダリ (Shift キー) のバインドを定義します。各文字はマウスボタン (右、中、第 4、第 5) を表します。それぞれ「+」でデバイスで進む、「-」で無視、「b」で戻る、「h」でホーム、「s」でアプリを切り替え、「n」で通知パネルの展開に設定できます。例えば、--mouse-bind=bhsn:++++ はプライマリバインドが「戻る、ホーム、アプリの切り替え、通知」であり、セカンダリバインドがすべてデバイスへ進むことを意味します。", "preferences.input.mouse-bind.placeholder": "bhsn:++++", "preferences.input.keyboard.name": "キーボードモード", "preferences.input.keyboard.placeholder": "sdk", "preferences.input.keyboard.tips": "キーボード入力モードを設定します。", "preferences.input.keyboard.options[0].placeholder": "既定", "preferences.input.keyboard.options[1].placeholder": "デバイス上の UHID カーネルモジュールを使用して物理 HID キーボードをシミュレートします", "preferences.input.keyboard.options[2].placeholder": "AOAv2 プロトコルを使用して物理 HID キーボードをシミュレート", "preferences.input.keyboard.options[3].placeholder": "キーボード入力を無効にする", "preferences.input.keyboard.inject.name": "キーボードインジェクト", "preferences.input.keyboard.inject.placeholder": "既定", "preferences.input.keyboard.inject.tips": "キーボードテキストとして挿入する最初のオプションを設定します。", "preferences.input.keyboard.inject.options[0].placeholder": "文字をテキストとして挿入", "preferences.input.keyboard.inject.options[1].placeholder": "常に元のボタンイベントを強制的に挿入", "preferences.input.gamepad.name": "ゲームパッド", "preferences.input.gamepad.placeholder": "無効", "preferences.input.gamepad.tips": "このオプションを使用すると、ゲームパッド (PS4/PS5 または Xbox) をコンピューターに接続して Android ゲームをプレイできます。注意: プレイするゲームでゲームパッドの入力をサポートしている必要があります。", "preferences.camera.name": "カメラ", "preferences.camera.camera-facing.name": "カメラソース", "preferences.camera.camera-facing.placeholder": "デバイスカメラのソース", "preferences.camera.camera-facing.front": "フロントカメラ", "preferences.camera.camera-facing.back": "バックカメラ", "preferences.camera.camera-facing.external": "外部カメラ", "preferences.camera.camera-size.name": "カメラサイズ", "preferences.camera.camera-size.placeholder": "デバイスカメラのサイズ、形式: 1920x1080", "preferences.camera.camera-ar.name": "カメラのアスペクト比", "preferences.camera.camera-ar.placeholder": "デバイスカメラのアスペクト比、形式: 4:3、センサー、1.6 など", "preferences.camera.camera-fps.name": "カメラのフレームレート", "preferences.camera.camera-fps.placeholder": "デバイスカメラのフレームレート", "about.name": "バージョン情報", "about.description": "📱 Scrcpy は Android デバイスをグラフィカルに表示とコントロールを行います。Powered by Electron", "about.update": "更新を確認", "about.update-not-available": "最新のバージョンを使用しています", "about.update-error.title": "更新の確認に失敗しました", "about.update-error.message": "プロキシが必要になる可能性があります。リリースページから手動でダウンロードしますか？", "about.update-downloaded.title": "新しいバージョンをダウンロードしました", "about.update-downloaded.message": "再起動して今すぐ更新しますか？", "about.update-downloaded.confirm": "更新", "about.update-available.title": "更新が利用可能です", "about.update-available.confirm": "更新", "about.update.progress": "更新中...", "about.donate.title": "寄付", "about.donate.description": "このプロジェクトがお役に立てたのであれば、私にコーヒーをご馳走して改善へのエネルギーをください 😛", "about.docs.name": "ヘルプドキュメント", "desktop.shortcut.add": "デスクトップにショートカットを作成"}