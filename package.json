{"name": "escrcpy", "type": "module", "version": "1.30.2", "private": true, "packageManager": "pnpm@9.13.2+sha512.88c9c3864450350e65a33587ab801acf946d7c814ed1134da4a924f6df5a2120fd36b46aab68f7cd1d413149112d53c7db3a4136624cfd00ff1846a0c6cef48a", "description": "Scrcpy Powered by Electron", "author": "viarotel", "homepage": "https://github.com/viarotel-org/escrcpy", "main": "dist-electron/main.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "dev": "vite", "build": "vite build && electron-builder", "build:win": "vite build && electron-builder --win", "build:mac": "vite build && electron-builder --mac", "build:linux": "vite build && electron-builder --linux", "electron-fix": "npx electron-fix start", "svgo": "svgo --folder=src/icons/svgo --output=src/icons/svg --config=src/icons/svgo.config.js", "postinstall": "electron-builder install-app-deps", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "prepare": "husky"}, "dependencies": {"vue-draggable-resizable": "^3.0.0"}, "devDependencies": {"@antfu/eslint-config": "4.16.2", "@devicefarmer/adbkit": "3.3.8", "@electron-toolkit/preload": "3.0.1", "@electron-toolkit/utils": "3.0.0", "@electron/remote": "2.1.2", "@element-plus/icons-vue": "^2.3.1", "@intlify/unplugin-vue-i18n": "4.0.0", "@unocss/reset": "0.62.3", "@unocss/transformer-directives": "0.62.3", "@viarotel-org/unocss-preset-shades": "0.8.2", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "4.1.1", "@vueuse/core": "10.9.0", "bonjour-service": "1.3.0", "create-desktop-shortcuts": "1.11.0", "dayjs": "1.11.11", "electron": "33.0.2", "electron-builder": "25.1.8", "electron-context-menu": "4.0.4", "electron-find-in-page": "1.0.8", "electron-log": "5.2.0", "electron-store": "9.0.0", "electron-updater": "6.3.9", "element-plus": "2.9.1", "eslint": "9.31.0", "fix-path": "4.0.0", "fs-extra": "11.2.0", "husky": "9.0.11", "ip-regex": "5.0.0", "lodash-es": "4.17.21", "minimist": "1.2.8", "nanoid": "5.0.7", "pinia": "2.1.7", "pinia-plugin-persistedstate": "3.2.1", "pinyin-pro": "3.26.0", "postcss": "8.4.38", "postcss-nested": "6.0.1", "postcss-scss": "4.0.9", "qrcode": "1.5.4", "rimraf": "6.0.1", "simple-git": "3.27.0", "swapy": "1.0.5", "tree-kill": "1.2.2", "unocss": "0.62.3", "unplugin-auto-import": "0.18.3", "unplugin-vue-components": "0.27.4", "unplugin-vue-router": "0.10.9", "vite": "5.1.5", "vite-plugin-electron": "0.28.8", "vite-plugin-electron-renderer": "0.14.6", "vite-svg-loader": "5.1.0", "vitepress": "1.6.3", "vitepress-i18n": "1.3.3", "vitepress-sidebar": "1.31.1", "vue": "3.4.21", "vue-command": "35.2.1", "vue-i18n": "9.13.1", "vue-router": "4.5.0", "vue-screen": "2.4.2", "which": "4.0.0"}}