---
title: Milestones
---

# Milestones

Priority from high to low:

- Improved logo ✅
- Software update feature ✅
- Record and save audio/video ✅
- Device quick interaction control bar ✅
- Custom Adb and Scrcpy dependencies ✅
- Custom device names ✅
- Export and import preferences ✅
- Individual device configuration ✅
- macOS and Linux support ✅
- Internationalization ✅
- Dark mode ✅
- Reverse tethering (Gnirehtet) ✅
- Camera mirroring ✅
- Multi-screen collaboration ✅
- File push, screen rotation, audio control ✅
- Batch connect historical devices ✅
- Built-in terminal ✅
- Auto-execute mirroring ✅
- Flexible mirroring launch ✅
- Batch processing ✅
- Scheduled tasks ✅
- Graphical file manager ✅
- Floating control bar ✅
- Enhanced recording ✅
- Start APP(Multi-threaded) ✅
- Main window edge hidden ✅
- Group devices (by filtering remarks) ✅
- Improved history device connection experience ✅  
- File management supports uploading directories ✅
- Support adding open APP to desktop shortcut (Windows only) ✅
- Support batch startup of devices ✅
- Improve the way to set the mirror window position and size 🚧
- Improve the experience of batch connection of devices 🚧
- Support graphical script editing tool 🚧